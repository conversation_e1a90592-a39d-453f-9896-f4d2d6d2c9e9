<template>
  <div class="data-view-wrapper">
    <!-- 权限不足提示 -->
    <div v-if="!hasDataViewPermission()" class="permission-denied">
      <a-result
        status="403"
        title="权限不足"
        sub-title="抱歉，您没有权限访问数据视图页面"
      >
        <template #extra>
          <a-button type="primary" @click="$router.go(-1)">返回</a-button>
        </template>
      </a-result>
    </div>

    <!-- 项目选择区域 -->
    <div v-else-if="!showWorkspace" class="project-selection-area">
      <div
        v-for="group in projectGroups"
        :key="group.key"
        class="project-group"
      >
        <!-- 分组标题行 -->
        <a-row
          type="flex"
          justify="space-between"
          align="middle"
          class="group-header"
        >
          <a-col>
            <h3 class="group-title">{{ group.title }}</h3>
          </a-col>
          <a-col v-if="group.children && group.children.length > 4 && checkGroupPermission(group)">
            <a-button
              type="link"
              @click="toggleGroupExpansion(group)"
              class="jump-button"
            >
              {{ expandedGroups.has(group.key) ? '收起' : '查看全部' }}
              <a-icon :type="expandedGroups.has(group.key) ? 'up' : 'down'" />
            </a-button>
          </a-col>
        </a-row>

        <!-- 项目卡片行 -->
        <a-row :gutter="[16, 16]" class="project-cards">
          <a-col
            v-for="project in getDisplayProjects(group)"
            :key="project.key"
            :span="responsiveSpan"
          >
            <a-card
              hoverable
              :class="[
                'project-card',
                {
                  disabled: !checkProjectPermission(project)
                }
              ]"
            >
              <div class="card-content">
                <!-- 卡片标题 -->
                <div class="project-name">{{ project.title }}</div>
                <!-- 卡片按钮 -->
                <div class="project-actions">
                  <a-button
                    type="primary"
                    size="middle"
                    :disabled="!checkProjectPermission(project)"
                    @click="handleProjectJump(project)"
                    class="custom-text-button"
                  >
                    查看详情
                  </a-button>
                </div>
              </div>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 工作台内容 -->
    <div class="workspace-content" v-else-if="showWorkspace && hasDataViewPermission()">
      <div class="workspace-header">
        <h2 class="workspace-title">{{ (selectedProjectData && selectedProjectData.title) || '工作台' }}</h2>
        <a-button
          type="link"
          @click="handleBackToParent"
          class="back-button"
        >
          <a-icon type="arrow-left" /> 返回项目选择
        </a-button>
      </div>
      <Index
        :selected-project="selectedProject"
        :selected-project-data="selectedProjectData"
        :selected-data-source="selectedDataSource"
        @data-source-change="handleDataSourceChange"
        @back-to-parent="handleBackToParent"
      />
    </div>
  </div>
</template>

<script>
import Index from './index.vue';
import { Http } from '@/utils/request';

export default {
  name: 'DataView',
  components: {
    Index
  },
  data() {
    // 获取用户角色信息，用于权限控制
    let role = this.$store.state.account.user.role;
    return {
      role,
      projectGroups: [],
      selectedProject: null,
      selectedProjectData: null, // 存储选中项目的完整数据
      selectedDataSource: null,
      loading: false,
      showWorkspace: false, // 控制是否显示工作台
      expandedGroups: new Set() // 记录展开显示全部卡片的分组
    };
  },
  computed: {
    // 响应式计算span值
    responsiveSpan() {
      // 这里可以根据实际需求调整断点
      if (typeof window !== 'undefined') {
        const width = window.innerWidth;
        if (width <= 576) return 24; // 小屏幕：1个一行
        if (width <= 768) return 12; // 中屏幕：2个一行
        if (width <= 1200) return 8; // 大屏幕：3个一行
        return 6; // 超大屏幕：4个一行
      }
      return 6; // 默认值
    }
  },
  mounted() {
    // 监听窗口大小变化以触发响应式重新计算
    this.handleResize = () => {
      this.$forceUpdate(); // 强制更新以重新计算responsiveSpan
    };
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 清理事件监听器
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },
  created() {
    // 检查用户是否有数据查看权限
    if (!this.hasDataViewPermission()) {
      this.$message.error('您没有权限访问数据视图页面');
      // 可以选择跳转到其他页面或显示权限不足页面
      // this.$router.push({ path: '/exception/403' });
      return;
    }

    this.loadProjectData();
  },
  methods: {
    // 获取项目参数配置（复用Todo组件的逻辑）
    getProjectParams() {
      return {
        url: '/sqlreview/report/report_all_project_group',
        reqParams: {},
        placeholder: '请选择项目或项目组',
        beforeLoaded: res => {
          const arr = res.map(item => {
            return {
              ...item,
              title: item.label,
              key: item.value,
              children:
                item.children &&
                item.children.map(el => {
                  return {
                    ...el,
                    title: el.label,
                    key: el.value
                  };
                })
            };
          });
          return arr;
        }
      };
    },

    // 加载项目数据
    async loadProjectData() {
      this.loading = true;
      try {
        // const params = this.getProjectParams();
        // const response = await Http({
        //   url: params.url,
        //   method: 'get',
        //   params: params.reqParams
        // });

        // if (response.data && response.data.code === 0) {
        //   const processedData = params.beforeLoaded(response.data.data || []);
        //   this.projectGroups = this.processProjectData(processedData);
        // }

        // 模拟数据 - 符合 [{value: '', label: '', children: [{value: '', label: ''}]}] 格式
        const mockData = [
          {
            value: 'project_group_1',
            label: '核心业务系统',
            children: [
              {
                value: 'project_1_1',
                label: '用户管理系统'
              },
              {
                value: 'project_1_2',
                label: '订单管理系统'
              },
              {
                value: 'project_1_3',
                label: '支付管理系统'
              },
              {
                value: 'project_1_4',
                label: '库存管理系统'
              },
              {
                value: 'project_1_5',
                label: '供应链管理系统'
              },
              {
                value: 'project_1_6',
                label: '物流管理系统'
              },
              {
                value: 'project_1_7',
                label: '客服管理系统'
              },
              {
                value: 'project_1_8',
                label: '营销管理系统'
              }
            ]
          },
          {
            value: 'project_group_2',
            label: '数据分析平台',
            children: [
              {
                value: 'project_2_1',
                label: '实时数据监控'
              },
              {
                value: 'project_2_2',
                label: '业务报表系统'
              }
            ]
          },
          {
            value: 'project_group_3',
            label: '运维工具',
            children: [
              {
                value: 'project_3_1',
                label: '日志分析系统'
              },
              {
                value: 'project_3_2',
                label: '性能监控平台'
              },
              {
                value: 'project_3_3',
                label: '自动化部署工具'
              }
            ]
          },
          {
            value: 'standalone_project_1',
            label: '独立项目 - 文档管理系统',
            children: []
          },
          {
            value: 'standalone_project_2',
            label: '独立项目 - 消息通知服务',
            children: []
          }
        ];

        // 使用 beforeLoaded 方法处理数据
        const params = this.getProjectParams();
        const processedData = params.beforeLoaded(mockData);
        this.projectGroups = this.processProjectData(processedData);
      } catch (error) {
        console.error('加载项目数据失败:', error);
        this.$message.error('加载项目数据失败');
      } finally {
        this.loading = false;
      }
    },

    // 处理项目数据，按是否有子项目分组
    processProjectData(data) {
      const groups = [];

      // 根据权限过滤项目数据
      const filteredData = this.filterProjectsByPermission(data);

      // 有子项目的分组
      const withChildren = filteredData.filter(
        item => item.children && item.children.length > 0
      );
      withChildren.forEach(item => {
        // 对子项目也进行权限过滤
        const filteredChildren = this.filterProjectsByPermission(item.children);
        if (filteredChildren.length > 0) {
          groups.push({
            key: item.key,
            title: item.title,
            children: filteredChildren,
            hasChildren: true
          });
        }
      });

      // 没有子项目的，归为"其它"分组
      const withoutChildren = filteredData.filter(
        item => !item.children || item.children.length === 0
      );
      if (withoutChildren.length > 0) {
        groups.push({
          key: 'others',
          title: '其它',
          children: withoutChildren,
          hasChildren: false
        });
      }

      return groups;
    },

    // 根据用户权限过滤项目列表
    filterProjectsByPermission(projects) {
      if (!projects || !Array.isArray(projects)) {
        return [];
      }

      return projects.filter(project => {
        // 使用权限系统检查项目访问权限
        // 如果项目有特定的权限码，使用$permission检查
        if (project.permission_code) {
          return this.$permission.project(project.permission_code);
        }

        // 如果项目有角色限制，检查用户角色
        if (project.visible_roles && Array.isArray(project.visible_roles)) {
          return project.visible_roles.includes(this.role);
        }

        // 默认情况下，根据用户角色进行基本权限控制
        // developer 和 leader 角色可能有不同的项目访问权限
        if (['developer', 'leader'].includes(this.role)) {
          // 开发者和组长可能只能看到自己相关的项目
          // 这里可以根据具体业务需求调整
          return true; // 暂时允许访问所有项目
        }

        // 管理员和DBA默认可以访问所有项目
        return true;
      });
    },

    // 获取要显示的项目（根据展开状态决定显示数量）
    getDisplayProjects(group) {
      if (!group.children) return [];
      // 如果分组已展开，显示全部项目；否则最多显示4个
      return this.expandedGroups.has(group.key) ? group.children : group.children.slice(0, 4);
    },

    // 处理项目跳转（进入工作台）
    handleProjectJump(project) {
      // 检查项目访问权限
      if (!this.checkProjectPermission(project)) {
        this.$message.warning('您没有权限访问该项目');
        return;
      }

      // 设置选中的项目数据并进入工作台
      this.selectedProject = project.value; // 使用project.value作为treeId
      this.selectedProjectData = project; // 保存完整的项目数据
      this.selectedDataSource = null; // 重置数据源选择
      this.showWorkspace = true; // 显示工作台

      // 触发数据刷新事件
      this.$emit('project-change', project.value);
    },

    // 切换分组展开/收起状态
    toggleGroupExpansion(group) {
      // 检查分组访问权限
      if (!this.checkGroupPermission(group)) {
        this.$message.warning('您没有权限访问该项目组');
        return;
      }

      // 切换展开状态
      if (this.expandedGroups.has(group.key)) {
        this.expandedGroups.delete(group.key);
      } else {
        this.expandedGroups.add(group.key);
      }

      // 触发响应式更新
      this.$forceUpdate();
    },

    // 检查项目权限
    checkProjectPermission(project) {
      // 使用权限系统检查项目访问权限
      if (project.permission_code) {
        return this.$permission.project(project.permission_code);
      }

      // 如果项目有角色限制，检查用户角色
      if (project.visible_roles && Array.isArray(project.visible_roles)) {
        return project.visible_roles.includes(this.role);
      }

      // 默认权限检查
      return true;
    },

    // 检查项目组权限
    checkGroupPermission(group) {
      // 使用权限系统检查项目组访问权限
      if (group.permission_code) {
        return this.$permission.projectGroup(group.permission_code);
      }

      // 如果项目组有角色限制，检查用户角色
      if (group.visible_roles && Array.isArray(group.visible_roles)) {
        return group.visible_roles.includes(this.role);
      }

      // 默认权限检查
      return true;
    },

    // 处理数据源变化
    handleDataSourceChange(dataSource) {
      this.selectedDataSource = dataSource;
    },

    // 处理返回父级卡片
    handleBackToParent() {
      this.selectedProject = null;
      this.selectedProjectData = null;
      this.selectedDataSource = null;
      this.showWorkspace = false; // 隐藏工作台，回到项目选择页面
    },

    // 显示权限不足提示
    showPermissionDeniedMessage(type = 'project') {
      const messages = {
        project: '您没有权限访问该项目',
        group: '您没有权限访问该项目组',
        data: '您没有权限查看项目数据'
      };
      this.$message.warning(messages[type] || '权限不足');
    },

    // 检查用户是否有数据查看权限
    hasDataViewPermission() {
      // 调试信息：打印当前权限状态
      // console.log('当前用户角色:', this.role);
      // console.log('权限页面列表:', this.$store.state.auth.pages);

      // 临时禁用权限检查，直接返回 true
      return true;

      // // 检查页面级权限
      // const auth = this.$store.state.auth;
      // if (auth && auth.pages) {
      //   const hasPagePermission = auth.pages.some(page =>
      //     page.id.includes('data-view') || page.id.includes('dataview')
      //   );
      //   if (!hasPagePermission) {
      //     return false;
      //   }
      // }

      // // 检查角色权限
      // const allowedRoles = ['admin', 'dba', 'leader', 'developer'];
      // return allowedRoles.includes(this.role);
    }
  }
};
</script>

<style lang="less" scoped>
.data-view-wrapper {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.permission-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: #fff;
  border-radius: 8px;
  margin: 24px 0;
}

.project-selection-area {
  margin-bottom: 24px;
}

.project-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  margin-bottom: 16px;
  padding: 0 8px;
}

.group-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.jump-button {
  padding: 0;
  font-size: 14px;

  &:hover {
    color: #1890ff;
  }
}

.project-cards {
  .ant-col {
    display: flex;
  }
}

.project-card {
  width: 100%;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #e8e8e8;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  position: relative;
  overflow: hidden;

  // 添加装饰性的渐变边框
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #1890ff, #52c41a, #faad14, #f5222d);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  // 添加装饰性的角标
  &::after {
    content: '';
    position: absolute;
    top: 12px;
    right: 12px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #52c41a;
    opacity: 0.6;
  }

  &:hover:not(.disabled) {
    box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
    transform: translateY(-4px);
    border-color: #1890ff;

    &::before {
      opacity: 1;
    }

    &::after {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  &.active {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);

    &::before {
      opacity: 1;
    }
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f5f5f5;

    .project-name {
      color: #bfbfbf;
    }

    &::after {
      background: #d9d9d9;
    }

    &:hover {
      box-shadow: none;
      transform: none;
      border-color: #e8e8e8;

      &::before {
        opacity: 0;
      }

      &::after {
        transform: none;
      }
    }
  }

  .ant-card-body {
    padding: 20px;
    position: relative;
    z-index: 1;
  }
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  min-height: 100px;
  justify-content: space-between;
  position: relative;

  // 添加装饰性图标
  &::before {
    content: '📊';
    position: absolute;
    top: -5px;
    right: 0;
    font-size: 24px;
    opacity: 0.3;
    transition: all 0.3s ease;
  }

  .project-card:hover & {
    &::before {
      opacity: 0.6;
      transform: rotate(10deg) scale(1.1);
    }
  }
}

.project-name {
  font-size: 18px;
  font-weight: bold;
  font-family: "Microsoft YaHei", "SimHei", "黑体", sans-serif;
  color: #262626;
  margin-bottom: 16px;
  line-height: 1.4;
  word-break: break-word;
  text-align: left;
  width: 100%;
  position: relative;
  padding-left: 12px;

  // 添加装饰性左边框
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    bottom: 2px;
    width: 4px;
    background: linear-gradient(180deg, #1890ff, #52c41a);
    border-radius: 2px;
    transition: all 0.3s ease;
  }

  .project-card:hover & {
    color: #1890ff;

    &::before {
      width: 6px;
      background: linear-gradient(180deg, #40a9ff, #73d13d);
    }
  }
}

.project-actions {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-start;

  .ant-btn {
    width: 70%;
  }

  .custom-text-button {
    border: 1px solid #91d5ff;
    border-radius: 40px;
    background-color: #bae7ff;
    color: #1890ff;
    transition: all 0.3s ease;

    &:hover {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #ffffff;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
    }

    &:focus {
      background-color: #1890ff;
      border-color: #1890ff;
      color: #ffffff;
    }
  }
}

.workspace-content {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.workspace-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 5px solid #f0f0f0;
  margin-bottom: 40px;
  background: #fafafa;

  .back-button {
    padding: 0;
    font-size: 14px;
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }
  }

  .workspace-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #262626;
  }
}

// 响应式设计 - span值通过JavaScript计算属性动态控制
@media (max-width: 768px) {
  .data-view-wrapper {
    padding: 16px;
  }

  .group-title {
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .data-view-wrapper {
    padding: 12px;
  }

  .project-group {
    margin-bottom: 24px;
  }
}
</style>
